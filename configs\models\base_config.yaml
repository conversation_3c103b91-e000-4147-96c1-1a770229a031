# Base Model Configuration Template
# This file defines the common structure for all model configurations

# Model Information
model_info:
  name: ""                    # Model name (e.g., "yolov8n", "yolov13n")
  version: ""                 # Model version
  family: ""                  # Model family (e.g., "yolov8", "yolov13")
  task: "detect"              # Task type: detect, segment, classify, pose
  description: ""             # Model description

# Model Architecture
architecture:
  backbone: ""                # Backbone architecture
  neck: ""                   # Neck architecture  
  head: ""                   # Head architecture
  input_size: [640, 640]     # Input image size [height, width]
  num_classes: 80            # Number of classes

# Model Scaling (for different model sizes)
scales:
  # [depth_multiple, width_multiple, max_channels]
  n: [0.33, 0.25, 1024]      # Nano
  s: [0.33, 0.50, 1024]      # Small
  m: [0.67, 0.75, 768]       # Medium
  l: [1.00, 1.00, 512]       # Large
  x: [1.00, 1.25, 512]       # Extra Large

# Training Configuration
training:
  # Optimizer settings
  optimizer: "AdamW"          # Optimizer type
  lr0: 0.01                  # Initial learning rate
  lrf: 0.01                  # Final learning rate factor
  momentum: 0.937            # Momentum
  weight_decay: 0.0005       # Weight decay
  warmup_epochs: 3.0         # Warmup epochs
  warmup_momentum: 0.8       # Warmup momentum
  
  # Training parameters
  epochs: 100                # Number of training epochs
  batch_size: 16             # Batch size
  imgsz: 640                 # Training image size
  
  # Data augmentation
  hsv_h: 0.015              # HSV-Hue augmentation
  hsv_s: 0.7                # HSV-Saturation augmentation
  hsv_v: 0.4                # HSV-Value augmentation
  degrees: 0.0              # Rotation degrees
  translate: 0.1            # Translation
  scale: 0.5                # Scale
  shear: 0.0                # Shear
  perspective: 0.0          # Perspective
  flipud: 0.0               # Vertical flip probability
  fliplr: 0.5               # Horizontal flip probability
  mosaic: 1.0               # Mosaic probability
  mixup: 0.0                # Mixup probability
  copy_paste: 0.1           # Copy-paste probability
  
  # Loss weights
  box: 7.5                  # Box loss weight
  cls: 0.5                  # Classification loss weight
  dfl: 1.5                  # DFL loss weight
  
  # Other settings
  amp: true                 # Automatic Mixed Precision
  cache: false              # Cache images
  device: "0"               # Device (GPU/CPU)
  workers: 4                # Number of workers
  seed: 42                  # Random seed
  deterministic: true       # Deterministic training
  save_period: 10           # Save checkpoint every N epochs

# Validation Configuration
validation:
  conf: 0.001               # Confidence threshold
  iou: 0.7                  # IoU threshold for NMS
  max_det: 300              # Maximum detections per image
  save_json: false          # Save results in JSON format
  save_hybrid: false        # Save hybrid labels
  plots: true               # Generate plots

# Export Configuration
export:
  format: "onnx"            # Export format
  imgsz: 640                # Export image size
  half: false               # FP16 quantization
  dynamic: false            # Dynamic axes
  simplify: true            # Simplify ONNX model
  opset: 12                 # ONNX opset version

# Evaluation Metrics
metrics:
  primary: "mAP50-95"       # Primary metric
  secondary: ["mAP50", "mAP75", "precision", "recall", "f1"]
  
# Hardware Requirements
hardware:
  min_gpu_memory: "4GB"     # Minimum GPU memory
  recommended_gpu: "RTX 3080" # Recommended GPU
  cpu_cores: 4              # Minimum CPU cores
  ram: "8GB"                # Minimum RAM

# Paths (will be overridden by specific configs)
paths:
  weights: ""               # Path to pretrained weights
  config: ""                # Path to model config file
  data: ""                  # Path to dataset config
